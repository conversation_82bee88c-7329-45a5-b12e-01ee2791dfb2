      <header class="site-navbar py-4 js-sticky-header site-navbar-target" role="banner">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-6 col-xl-2">
                        <h1 class="mb-0 site-logo m-0 p-0"><a href="index.html" class="mb-0">LegalSpot</a></h1>
                    </div>
                    <div class="col-12 col-md-10 d-none d-xl-block">
                        <nav class="site-navigation position-relative text-right" role="navigation">
                            <ul class="site-menu main-menu js-clone-nav mr-auto d-none d-lg-block">
                                <li><a href="#home-section" class="nav-link">Accueil</a></li>
                                <li><a href="#lots-section" class="nav-link">Lots</a></li>
                                <li><a href="#howitworks-section" class="nav-link">Comment ça marche</a></li>
                        <li class="nav-item position-relative">
                            <a href="#" class="nav-link" id="ville-toggle">Ville</a>
                            <div id="dropdown-villes" class="dropdown-ville-cols"></div>
                        </li>


                        <li><a href="#about-section" class="nav-link">À propos</a></li>
                                <li><a href="#news-section" class="nav-link">Actualités</a></li>
                                <li><a href="#contact-section" class="nav-link">Contact</a></li>
                            </ul>
                        </nav>
                    </div>
                    <div class="col-6 d-inline-block d-xl-none ml-md-0 py-3"><a href="#" class="site-menu-toggle js-menu-toggle text-black float-right"><span class="icon-menu h3"></span></a></div>
                </div>
            </div>
        </header>
<style>
    .dropdown-ville-cols {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: #fff;
        padding: 20px;
        border: 1px solid #ccc;
        width: 700px;
        display: flex;
        justify-content: space-between;
        gap: 20px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .ville-col {
        flex: 1;
        max-height: 250px;
        overflow-y: auto;
    }

        .ville-col h6 {
            margin: 0 0 10px;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            color: #fff;
            padding: 5px;
            border-radius: 4px;
        }

        .ville-col.nord h6 {
            background-color: #f57c00;
        }

        .ville-col.centre h6 {
            background-color: #fbc02d;
            color: #333;
        }

        .ville-col.sud h6 {
            background-color: #d32f2f;
        }

        .ville-col ul {
            list-style: none;
            padding-left: 0;
            margin: 0;
        }

            .ville-col ul li {
                background: #f5f5f5;
                margin: 5px;
                padding: 6px 10px;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.2s;
                text-align: center;
            }

                .ville-col ul li:hover {
                    background-color: #e0e0e0;
                }
</style>
