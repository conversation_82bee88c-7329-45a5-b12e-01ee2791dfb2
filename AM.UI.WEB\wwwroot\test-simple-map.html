<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carte Simple de la Tunisie</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        #tunisia-map {
            height: 600px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
            background: #ffffff;
            position: relative;
        }
        
        .legend {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .nord { background-color: #2980b9; }
        .centre { background-color: #27ae60; }
        .sud { background-color: #e67e22; }
        
        /* Masquer les contrôles par défaut de Leaflet */
        .leaflet-control-attribution {
            display: none;
        }
        
        /* Style pour les popups */
        .leaflet-popup-content-wrapper {
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .leaflet-popup-content {
            margin: 15px;
        }
        
        .leaflet-popup-content h5 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .leaflet-popup-content p {
            margin-bottom: 10px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇹🇳 Carte Interactive de la Tunisie</h1>
        
        <div class="row">
            <div class="col-lg-9">
                <div id="tunisia-map"></div>
            </div>
            
            <div class="col-lg-3">
                <div class="legend">
                    <h6><strong>Régions de Tunisie</strong></h6>
                    
                    <div class="legend-item">
                        <div class="legend-color nord"></div>
                        <span>Nord</span>
                    </div>
                    
                    <div class="legend-item">
                        <div class="legend-color centre"></div>
                        <span>Centre</span>
                    </div>
                    
                    <div class="legend-item">
                        <div class="legend-color sud"></div>
                        <span>Sud</span>
                    </div>
                    
                    <hr>
                    
                    <p class="small text-muted">
                        Cliquez sur un gouvernorat pour le découvrir
                    </p>
                    
                    <div class="text-center">
                        <span class="badge bg-primary">24 Gouvernorats</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Données GeoJSON de la Tunisie -->
    <script src="js/tunisia-real-geojson.js"></script>
    
    <!-- Script de la carte -->
    <script src="js/tunisia-simple-map.js"></script>
</body>
</html>
