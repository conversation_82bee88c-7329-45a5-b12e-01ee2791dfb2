<!DOCTYPE html>
<html lang="fr">
<head>
    <title>LegalSpot</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">



</head>
<body data-spy="scroll" data-target=".site-navbar-target" data-offset="300">
    <div class="site-wrap">

        @Html.Partial("_Nav")
        <div class="site-blocks-cover overlay" style="background-image: url(images/hero_1.jpg); background-size:cover; background-position:center;" data-aos="fade" id="home-section">
            <div class="container">
                <div class="row align-items-center justify-content-center">
                    <div class="col-md-7 mt-lg-5 text-center">
                        <h1 class="display-4 font-weight-bold">Toutes les annonces importantes en un seul endroit.</h1>
                        <p class="mb-5 lead"> Immobilier, voitures, enchères, publications légales… Accédez facilement aux informations officielles et ne manquez plus jamais une opportunité.</p>
                        <a href="#lots-section" class="btn btn-lg btn-primary shadow">Voir les lots disponibles</a>
                    </div>
                </div>
            </div>

            <a href="#tunisia-map-section" class="smoothscroll arrow-down"><span class="icon-arrow_downward"></span></a>
        </div>
        @Html.Partial("_tunisia_map")


        @Html.Partial("_Lots_disponibles")

       
        @Html.Partial("_apropos")

        @Html.Partial("_services")




        @Html.Partial("_timoiniage")




        @Html.Partial("_actualite")







        @Html.Partial("_contact")










        @Html.Partial("_footer")





    </div> <!-- .site-wrap -->
    <script>
        const toggle = document.getElementById('ville-toggle');
        const dropdown = document.getElementById('dropdown-villes');

        fetch('/data/gouvernorats.json')
          .then(res => res.json())
          .then(data => {
            for (const region in data) {
              const col = document.createElement('div');
              col.className = `ville-col ${region.toLowerCase()}`;

              const title = document.createElement('h6');
              title.textContent = region;
              col.appendChild(title);

              const listDiv = document.createElement('div');
              listDiv.className = 'ville-list';

              const ul = document.createElement('ul');
              data[region].forEach(ville => {
                const li = document.createElement('li');
                li.textContent = ville;
                ul.appendChild(li);
              });

              listDiv.appendChild(ul);
              col.appendChild(listDiv);
              dropdown.appendChild(col);
            }
          });

        // Affichage au clic
        toggle.addEventListener('click', function(e) {
          e.preventDefault();
          dropdown.style.display = (dropdown.style.display === 'flex') ? 'none' : 'flex';
        });

        // Fermer au clic extérieur
        document.addEventListener('click', function(e) {
          if (!toggle.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.style.display = 'none';
          }
        });
    </script>

</body>
</html>