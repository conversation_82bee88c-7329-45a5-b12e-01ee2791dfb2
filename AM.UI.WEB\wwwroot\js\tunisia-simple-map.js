/**
 * Carte simple de la Tunisie avec gouvernorats cliquables
 * Fond blanc, forme réelle de la Tunisie
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si Leaflet est chargé
    if (typeof L === 'undefined') {
        console.error('Leaflet n\'est pas chargé.');
        return;
    }

    // Initialisation de la carte avec fond blanc
    var map = L.map('tunisia-map', {
        center: [34.0, 9.0],
        zoom: 7,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        dragging: true,
        attributionControl: false
    });

    // Pas de tuiles de fond - fond blanc simple

    var selectedGovernorat = null;

    // Couleurs simples pour les gouvernorats
    function getGovernorateColor(region, isSelected = false, isHovered = false) {
        if (isSelected) return '#3498db';  // Bleu pour sélection
        if (isHovered) return '#85c1e9';   // Bleu clair pour survol
        
        // Couleurs par région
        switch(region) {
            case 'nord': return '#2980b9';     // Bleu foncé
            case 'centre': return '#27ae60';   // Vert
            case 'sud': return '#e67e22';      // Orange
            default: return '#95a5a6';         // Gris
        }
    }

    // Créer la couche des gouvernorats
    var governorateLayer = L.geoJSON(tunisiaRealGeoJSON, {
        style: function(feature) {
            var region = feature.properties.region;
            var isSelected = selectedGovernorat === feature.properties.name;

            return {
                fillColor: getGovernorateColor(region, isSelected),
                weight: 2,
                opacity: 1,
                color: '#ffffff',  // Bordure blanche
                dashArray: '',
                fillOpacity: 0.8
            };
        },
        onEachFeature: function(feature, layer) {
            var governoratName = feature.properties.name;
            var region = feature.properties.region;

            // Popup simple
            var popupContent = `
                <div style="text-align: center; min-width: 200px;">
                    <h5>${governoratName}</h5>
                    <p>Région: ${region}</p>
                    <button class="btn btn-primary btn-sm" onclick="selectGovernorat('${governoratName}')">
                        Sélectionner
                    </button>
                </div>
            `;

            layer.bindPopup(popupContent, {
                maxWidth: 250,
                className: 'simple-popup'
            });

            // Événements de survol
            layer.on({
                mouseover: function(e) {
                    if (selectedGovernorat !== governoratName) {
                        layer.setStyle({
                            fillColor: getGovernorateColor(region, false, true),
                            weight: 3
                        });
                    }
                },
                mouseout: function(e) {
                    if (selectedGovernorat !== governoratName) {
                        layer.setStyle({
                            fillColor: getGovernorateColor(region),
                            weight: 2
                        });
                    }
                },
                click: function(e) {
                    selectGovernorat(governoratName);
                }
            });
        }
    }).addTo(map);

    // Fonction pour sélectionner un gouvernorat
    window.selectGovernorat = function(governoratName) {
        selectedGovernorat = governoratName;
        
        // Mettre à jour les styles
        governorateLayer.eachLayer(function(layer) {
            var feature = layer.feature;
            var region = feature.properties.region;
            var isSelected = feature.properties.name === governoratName;
            
            layer.setStyle({
                fillColor: getGovernorateColor(region, isSelected),
                weight: isSelected ? 3 : 2
            });
        });

        // Afficher les informations
        updateGovernorateInfo(governoratName);
        
        // Centrer sur le gouvernorat
        var feature = tunisiaRealGeoJSON.features.find(f => f.properties.name === governoratName);
        if (feature) {
            var bounds = L.geoJSON(feature).getBounds();
            map.fitBounds(bounds, {
                padding: [20, 20],
                animate: true
            });
        }
    };

    // Mettre à jour les informations du gouvernorat
    function updateGovernorateInfo(governoratName) {
        var infoDiv = document.getElementById('governorate-info');
        if (!infoDiv) {
            // Créer le div d'informations s'il n'existe pas
            infoDiv = document.createElement('div');
            infoDiv.id = 'governorate-info';
            infoDiv.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                z-index: 1000;
                min-width: 200px;
            `;
            document.body.appendChild(infoDiv);
        }

        infoDiv.innerHTML = `
            <h6>${governoratName}</h6>
            <p>Gouvernorat sélectionné</p>
            <button class="btn btn-sm btn-secondary" onclick="clearSelection()">Effacer</button>
        `;
        infoDiv.style.display = 'block';
    }

    // Effacer la sélection
    window.clearSelection = function() {
        selectedGovernorat = null;
        
        // Remettre les couleurs par défaut
        governorateLayer.eachLayer(function(layer) {
            var feature = layer.feature;
            var region = feature.properties.region;
            
            layer.setStyle({
                fillColor: getGovernorateColor(region),
                weight: 2
            });
        });

        // Masquer les informations
        var infoDiv = document.getElementById('governorate-info');
        if (infoDiv) {
            infoDiv.style.display = 'none';
        }

        // Revenir à la vue d'ensemble
        map.setView([34.0, 9.0], 7);
    };

    // Ajuster la vue initiale pour montrer toute la Tunisie
    setTimeout(function() {
        map.fitBounds(governorateLayer.getBounds(), {
            padding: [20, 20]
        });
    }, 100);
});
